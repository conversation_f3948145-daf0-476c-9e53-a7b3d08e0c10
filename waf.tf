resource "aws_wafv2_web_acl" "main_waf_web_acl" {
  name        = "${terraform.workspace}-waf"
  scope       = "REGIONAL"
  description = "Main Web Application Firewall for apps Edready, Portal, and ART. Associates to load balancers."
  default_action {
    allow {}
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "waf-main-metrics"
    sampled_requests_enabled   = true
  }

  rule {
    name     = "Main-AWSManagedRulesAmazonIpReputationList"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "Main-AWSManagedRulesAmazonIpReputationList"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "Main-AWSManagedRulesCommonRuleSet"
    priority = 0

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"

        rule_action_override {
          name = "NoUserAgent_HEADER"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "SizeRestrictions_QUERYSTRING"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "SizeRestrictions_BODY"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "SizeRestrictions_URIPATH"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "RestrictedExtensions_URIPATH"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "RestrictedExtensions_QUERYARGUMENTS"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "CrossSiteScripting_COOKIE"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "CrossSiteScripting_QUERYARGUMENTS"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "CrossSiteScripting_BODY"
          action_to_use {
            count {}
          }
        }
        rule_action_override {
          name = "CrossSiteScripting_URIPATH"
          action_to_use {
            count {}
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "Main-AWSManagedRulesCommonRuleSet-metric"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "Main-AWSManagedRulesSQLiRuleSet"
    priority = 2

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "Main-AWSManagedRulesSQLiRuleSet-metric"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "Main-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 3

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"

        rule_action_override {
          name = "ExploitablePaths_URIPATH"
          action_to_use {
            count {}
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "Main-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "Block-Actuator-Endpoints"
    priority = 4

    action {
      block {}
    }

    statement {
      byte_match_statement {
        search_string         = "/actuator"
        field_to_match {
          uri_path {}
        }
        text_transformation {
          priority = 0
          type     = "LOWERCASE"
        }
        positional_constraint = "STARTS_WITH"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "Block-Actuator-Endpoints"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "Geographic-Restriction-US-Only"
    priority = 5

    action {
      block {}
    }

    statement {
      not_statement {
        statement {
          geo_match_statement {
            country_codes = ["US"]
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "Geographic-Restriction-US-Only"
      sampled_requests_enabled   = true
    }
  }

  tags = {
    "Name" = "${terraform.workspace}-waf-setup"
    "Env"  = terraform.workspace
  }
}

locals {
  # Because these load balancers do not exist on each environment, we have to do some logic on whether to include them in the current workspace
  extra_load_balancers = {
    development = terraform.workspace == "development" ? [] : []
    testing     = terraform.workspace == "testing" ? [aws_elastic_beanstalk_environment.edready_spanish[0].load_balancers[0]] : []
    staging     = terraform.workspace == "staging" ? [] : []
    production = terraform.workspace == "production" ? [
      "arn:aws:elasticloadbalancing:us-west-1:341268829071:loadbalancer/app/awseb--AWSEB-EYQuCPUcZdH9/bd56865a7219f34d",
      aws_elastic_beanstalk_environment.edready_spanish[0].load_balancers[0]
    ] : []
    production-dr = terraform.workspace == "production-dr" ? [] : []
    default       = []
  }

  default_load_balancers = [
    aws_elastic_beanstalk_environment.event_tracker.load_balancers[0],
    aws_elastic_beanstalk_environment.edready.load_balancers[0],
    aws_elastic_beanstalk_environment.portal.load_balancers[0]
  ]
}

resource "aws_wafv2_web_acl_association" "main_waf_web_acl_association" {
  # Sort the values so that the order is always the same, so Terraform won't try to swap order and make changes
  count        = length(sort(concat(local.extra_load_balancers[terraform.workspace], local.default_load_balancers)))
  resource_arn = sort(concat(local.extra_load_balancers[terraform.workspace], local.default_load_balancers))[count.index]
  web_acl_arn  = aws_wafv2_web_acl.main_waf_web_acl.arn
}

resource "aws_cloudwatch_log_group" "main_waf_cloudwatch_log_group" {
  # checkov:skip=CKV_AWS_338
  name              = "aws-waf-logs-${terraform.workspace}"
  kms_key_id        = aws_kms_key.main_waf_kms_key.arn
  retention_in_days = 90

  tags = {
    "Name" = "aws-waf-logs-${terraform.workspace}"
    "Env"  = terraform.workspace
  }
}

resource "aws_wafv2_web_acl_logging_configuration" "main_wafv2_web_acl_logging_configuration" {
  log_destination_configs = [aws_cloudwatch_log_group.main_waf_cloudwatch_log_group.arn]
  resource_arn            = aws_wafv2_web_acl.main_waf_web_acl.arn

  logging_filter {
    default_behavior = "DROP"

    filter {
      behavior = "KEEP"

      condition {
        action_condition {
          action = "COUNT"
        }
      }

      condition {
        action_condition {
          action = "BLOCK"
        }
      }
      requirement = "MEETS_ANY"
    }
  }
}

resource "aws_cloudwatch_log_resource_policy" "main_waf_cloudwatch_log_resource_policy" {
  policy_document = data.aws_iam_policy_document.main_waf_iam_policy_document.json
  policy_name     = "${terraform.workspace}-webacl-policy"
}

data "aws_iam_policy_document" "main_waf_iam_policy_document" {
  version = "2012-10-17"
  statement {
    effect = "Allow"
    principals {
      identifiers = ["delivery.logs.amazonaws.com"]
      type        = "Service"
    }
    actions   = ["logs:CreateLogStream", "logs:PutLogEvents"]
    resources = ["${aws_cloudwatch_log_group.main_waf_cloudwatch_log_group.arn}:*"]
  }
}

resource "aws_kms_key" "main_waf_kms_key" {
  description             = "KMS key for ${terraform.workspace} WAF logs"
  enable_key_rotation     = true
  deletion_window_in_days = 30

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowRootAccountFullAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      },
      "Action": "kms:*",
      "Resource": "*"
    },
    {
      "Sid": "AllowCloudWatchLogsEncryption",
      "Effect": "Allow",
      "Principal": {
        "Service": "logs.${data.aws_region.current.name}.amazonaws.com"
      },
      "Action": [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:GenerateDataKey",
        "kms:GenerateDataKeyWithoutPlaintext"
      ],
      "Resource": "*"
    }
  ]
}
EOF

  tags = {
    "Name" = "${terraform.workspace}-waf-kms-key"
    "Env"  = terraform.workspace
  }
}